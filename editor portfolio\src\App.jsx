import Navbar from './components/Navbar';
import HeroSection from './components/HeroSection';
import AboutSection from './components/AboutSection';
import PortfolioSection from './components/PortfolioSection';

function App() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      {/* Hero Section */}
      <HeroSection />

      {/* About Section */}
      <AboutSection />

      {/* Portfolio Section */}
      <PortfolioSection />

      {/* Other sections */}
      <div className="w-full space-y-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

          <section id="about" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">About Us Section</h2>
          </section>

          <section id="services" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Services Section</h2>
          </section>

          <section id="portfolio" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Portfolio Section</h2>
          </section>

          <section id="why-choose-us" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Why Choose Us Section</h2>
          </section>

          <section id="testimonials" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Testimonials Section</h2>
          </section>

          <section id="contact" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Contact Section</h2>
          </section>
        </div>
      </div>
    </div>
  )
}

export default App
