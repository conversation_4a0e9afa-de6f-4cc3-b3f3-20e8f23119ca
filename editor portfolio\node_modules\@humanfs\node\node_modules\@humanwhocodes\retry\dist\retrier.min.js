class RetryTask{id=Math.random().toString(36).slice(2);fn;error;timestamp=Date.now();lastAttempt=this.timestamp;resolve;reject;signal;constructor(t,e,r,s,i){this.fn=t,this.error=e,this.timestamp=Date.now(),this.lastAttempt=Date.now(),this.resolve=r,this.reject=s,this.signal=i}get age(){return Date.now()-this.timestamp}}class Retrier{#t=[];#e;#r;#s;#i;constructor(t,{timeout:e=6e4,maxDelay:r=100}={}){if("function"!=typeof t)throw new Error("Missing function to check errors");this.#i=t,this.#e=e,this.#r=r}retry(t,{signal:e}={}){let r;e?.throwIfAborted();try{r=t()}catch(t){return Promise.reject(new Error(`Synchronous error: ${t.message}`,{cause:t}))}return r&&"function"==typeof r.then?Promise.resolve(r).catch((r=>{if(!this.#i(r))throw r;return new Promise(((s,i)=>{this.#t.push(new RetryTask(t,r,s,i,e)),e?.addEventListener("abort",(()=>{i(e.reason)})),this.#o()}))})):Promise.reject(new Error("Result is not a promise."))}#o(){clearTimeout(this.#s),this.#s=void 0;const t=this.#t.shift();if(!t)return;const e=()=>{this.#s=setTimeout((()=>this.#o()),0)};return function(t,e){return t.age>e}(t,this.#e)?(t.reject(t.error),void e()):function(t,e){const r=Date.now()-t.lastAttempt,s=Math.max(t.lastAttempt-t.timestamp,1);return r>=Math.min(1.2*s,e)}(t,this.#r)?(t.lastAttempt=Date.now(),void Promise.resolve(t.fn()).then((e=>t.resolve(e))).catch((e=>{this.#i(e)?(t.lastAttempt=Date.now(),this.#t.push(t)):t.reject(e)})).finally((()=>this.#o()))):(this.#t.push(t),void e())}}export{Retrier};
