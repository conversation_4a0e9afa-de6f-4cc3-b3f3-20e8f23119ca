const AboutSection = () => {
  return (
    <section id="about" className="relative py-20 bg-gradient-to-br from-[#EEEEEE] to-white overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-10 w-20 h-20 border-2 border-[#08CB00] rounded-full opacity-20"></div>
        <div className="absolute bottom-32 left-16 w-16 h-16 bg-[#08CB00] rounded-lg transform rotate-45 opacity-10"></div>
        <div className="absolute top-1/2 right-1/4 w-12 h-12 bg-[#253900] rounded-full opacity-15"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold text-[#253900] mb-4">
            Your Ultimate{' '}
            <span className="relative text-[#08CB00]">
              AI Solution
              <svg 
                className="absolute -bottom-2 left-0 w-full h-3 text-[#08CB00]" 
                viewBox="0 0 200 12" 
                fill="none"
              >
                <path 
                  d="M5 8 Q100 2 195 8" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  fill="none"
                />
              </svg>
            </span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            This customization enhances the utility and adaptability of our video editing services, making it a 
            versatile tool across various professional and creative contexts.
          </p>
        </div>

        {/* Main content grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          
          {/* Left side - Feature cards */}
          <div className="space-y-6">
            
            {/* Budget Friendly Card */}
            <div className="bg-[#253900] rounded-2xl p-6 border-2 border-[#08CB00] transform hover:scale-105 transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-[#08CB00] rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-black" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white">Budget Friendly</h3>
              </div>
              <p className="text-gray-300">
                No in-app analytics. No middle servers. Your projects are sent directly to budget-conscious solutions.
              </p>
            </div>

            {/* User Management Card */}
            <div className="bg-[#253900] rounded-2xl p-6 border-2 border-[#08CB00] transform hover:scale-105 transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-[#08CB00] rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-black" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white">User Management</h3>
              </div>
              <p className="text-gray-300">
                Effortlessly configure your preferred workflow shortcuts, enabling seamless integration.
              </p>
              
              {/* Savings Goal Widget */}
              <div className="mt-6 bg-white rounded-xl p-4">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-semibold text-gray-800">Saving Goal</h4>
                  <span className="text-[#08CB00] text-sm font-medium">See reports</span>
                </div>
                <div className="text-2xl font-bold text-gray-800 mb-2">$25,267.50</div>
                <div className="text-sm text-gray-600 mb-3">Income this month</div>
                
                {/* Simple chart representation */}
                <div className="h-20 bg-gray-50 rounded-lg flex items-end justify-center p-2">
                  <div className="flex items-end space-x-1 h-full">
                    <div className="w-3 bg-gray-300 rounded-t" style={{height: '30%'}}></div>
                    <div className="w-3 bg-gray-300 rounded-t" style={{height: '45%'}}></div>
                    <div className="w-3 bg-gray-300 rounded-t" style={{height: '60%'}}></div>
                    <div className="w-3 bg-[#08CB00] rounded-t" style={{height: '80%'}}></div>
                    <div className="w-3 bg-gray-300 rounded-t" style={{height: '55%'}}></div>
                    <div className="w-3 bg-gray-300 rounded-t" style={{height: '40%'}}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Feature cards */}
          <div className="space-y-6">
            
            {/* Native, Fast & Powerful Card */}
            <div className="bg-[#253900] rounded-2xl p-6 border-2 border-[#08CB00] transform hover:scale-105 transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-[#08CB00] rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-black" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white">Native, Fast & Powerful</h3>
              </div>
              <p className="text-gray-300 mb-6">
                Offers a library of templates that you can leverage to get the most out of our video editing services.
              </p>
              
              {/* Expense tracking widget */}
              <div className="bg-white rounded-xl p-4">
                <h4 className="font-semibold text-gray-800 mb-4">Project Analytics</h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <div className="w-4 h-4 bg-blue-500 rounded"></div>
                      </div>
                      <div>
                        <div className="font-medium text-gray-800">Grocery</div>
                        <div className="text-sm text-gray-500">$200/$500</div>
                      </div>
                    </div>
                    <span className="text-sm text-gray-600">$500 Left</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-cyan-100 rounded-lg flex items-center justify-center mr-3">
                        <div className="w-4 h-4 bg-cyan-500 rounded-full"></div>
                      </div>
                      <div>
                        <div className="font-medium text-gray-800">Mobile Recharge</div>
                        <div className="text-sm text-gray-500">$100/$300</div>
                      </div>
                    </div>
                    <span className="text-sm text-gray-600">$800 Left</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <div className="w-4 h-4 bg-green-500 rounded"></div>
                      </div>
                      <div>
                        <div className="font-medium text-gray-800">Tea & Coffee</div>
                        <div className="text-sm text-gray-500">$50/$200</div>
                      </div>
                    </div>
                    <span className="text-sm text-gray-600">$900 Left</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <div className="w-4 h-4 bg-blue-500 rounded"></div>
                      </div>
                      <div>
                        <div className="font-medium text-gray-800">Wi-Fi Bill</div>
                        <div className="text-sm text-gray-500">$80/$200</div>
                      </div>
                    </div>
                    <span className="text-sm text-gray-600">$400 Left</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Financial Planning Card */}
            <div className="bg-[#253900] rounded-2xl p-6 border-2 border-[#08CB00] transform hover:scale-105 transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-[#08CB00] rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-black" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white">Creative Planning</h3>
              </div>
              <p className="text-gray-300">
                Not another 200MB Electron app. Natively integrate with your favorite creative tools and workflows.
              </p>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <button className="bg-[#08CB00] text-black px-8 py-4 rounded-lg font-semibold text-lg hover:bg-[#06A800] transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
            Learn More About Our Process
          </button>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
