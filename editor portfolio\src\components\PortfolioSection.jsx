import { useState, useEffect } from 'react';

const PortfolioSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Mock data for 20 portrait videos
  const portfolioVideos = Array.from({ length: 20 }, (_, index) => ({
    id: index + 1,
    title: `Creative Project ${index + 1}`,
    category: ['Social Media', 'Commercial', 'Music Video', 'Documentary', 'Corporate'][index % 5],
    duration: `${Math.floor(Math.random() * 3) + 1}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')}`,
    client: `Client ${String.fromCharCode(65 + (index % 26))}`,
    description: `Professional video editing showcasing ${['dynamic transitions', 'color grading', 'motion graphics', 'sound design', 'visual effects'][index % 5]}.`
  }));

  const videosPerSlide = 6;
  const totalSlides = Math.ceil(portfolioVideos.length / videosPerSlide);

  // Auto-play functionality
  useEffect(() => {
    if (isAutoPlaying) {
      const interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % totalSlides);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [isAutoPlaying, totalSlides]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
    setIsAutoPlaying(false);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
    setIsAutoPlaying(false);
  };

  const getCurrentVideos = () => {
    const startIndex = currentSlide * videosPerSlide;
    return portfolioVideos.slice(startIndex, startIndex + videosPerSlide);
  };

  return (
    <section id="portfolio" className="relative py-20 bg-gradient-to-br from-black via-[#253900] to-black overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        <div className="absolute top-32 left-20 w-32 h-32 border border-[#08CB00] rounded-full opacity-10 animate-spin-slow"></div>
        <div className="absolute bottom-40 right-16 w-24 h-24 bg-[#08CB00] rounded-lg transform rotate-45 opacity-5"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 border-2 border-[#08CB00] rounded-full opacity-5"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <div className="mb-4">
            <span className="inline-block px-4 py-2 bg-[#08CB00] text-black text-sm font-semibold rounded-full uppercase tracking-wide">
              Our Creative Works
            </span>
          </div>
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">
            Portfolio{' '}
            <span className="relative text-[#08CB00]">
              Showcase
              <svg 
                className="absolute -bottom-2 left-0 w-full h-3 text-[#08CB00]" 
                viewBox="0 0 200 12" 
                fill="none"
              >
                <path 
                  d="M5 8 Q100 2 195 8" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  fill="none"
                />
              </svg>
            </span>
          </h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto">
            Discover our latest video editing projects that showcase creativity, technical excellence, and storytelling mastery.
          </p>
        </div>

        {/* Carousel container */}
        <div className="relative">
          {/* Navigation buttons */}
          <button
            onClick={prevSlide}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 z-20 bg-[#08CB00] text-black w-12 h-12 rounded-full flex items-center justify-center hover:bg-[#06A800] transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 z-20 bg-[#08CB00] text-black w-12 h-12 rounded-full flex items-center justify-center hover:bg-[#06A800] transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>

          {/* Carousel content */}
          <div className="overflow-hidden rounded-2xl">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {Array.from({ length: totalSlides }).map((_, slideIndex) => (
                <div key={slideIndex} className="w-full flex-shrink-0">
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 p-4">
                    {portfolioVideos
                      .slice(slideIndex * videosPerSlide, (slideIndex + 1) * videosPerSlide)
                      .map((video) => (
                        <div
                          key={video.id}
                          className="group relative bg-[#253900] rounded-xl overflow-hidden border-2 border-transparent hover:border-[#08CB00] transition-all duration-300 transform hover:scale-105"
                        >
                          {/* Video placeholder */}
                          <div className="aspect-[9/16] bg-gradient-to-br from-gray-800 to-gray-900 relative overflow-hidden">
                            {/* Play button overlay */}
                            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <div className="w-16 h-16 bg-[#08CB00] rounded-full flex items-center justify-center transform scale-75 group-hover:scale-100 transition-transform duration-300">
                                <svg className="w-8 h-8 text-black ml-1" fill="currentColor" viewBox="0 0 20 20">
                                  <path d="M8 5v10l8-5-8-5z"/>
                                </svg>
                              </div>
                            </div>
                            
                            {/* Video info overlay */}
                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-3">
                              <div className="text-white text-sm font-medium mb-1">{video.title}</div>
                              <div className="flex justify-between items-center text-xs text-gray-300">
                                <span>{video.category}</span>
                                <span>{video.duration}</span>
                              </div>
                            </div>

                            {/* Category badge */}
                            <div className="absolute top-2 left-2">
                              <span className="bg-[#08CB00] text-black text-xs font-semibold px-2 py-1 rounded-full">
                                {video.category}
                              </span>
                            </div>
                          </div>

                          {/* Video details */}
                          <div className="p-3">
                            <h4 className="text-white font-semibold text-sm mb-1">{video.title}</h4>
                            <p className="text-gray-400 text-xs mb-2">{video.description}</p>
                            <div className="flex justify-between items-center text-xs text-gray-500">
                              <span>Client: {video.client}</span>
                              <span className="text-[#08CB00]">{video.duration}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Slide indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {Array.from({ length: totalSlides }).map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide 
                    ? 'bg-[#08CB00] w-8' 
                    : 'bg-gray-600 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>

          {/* Auto-play toggle */}
          <div className="flex justify-center mt-6">
            <button
              onClick={() => setIsAutoPlaying(!isAutoPlaying)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                isAutoPlaying 
                  ? 'bg-[#08CB00] text-black hover:bg-[#06A800]' 
                  : 'bg-gray-700 text-white hover:bg-gray-600'
              }`}
            >
              {isAutoPlaying ? 'Pause Auto-play' : 'Resume Auto-play'}
            </button>
          </div>
        </div>

        {/* Bottom stats */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-3xl font-bold text-[#08CB00] mb-2">500+</div>
            <div className="text-gray-300">Projects Completed</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-[#08CB00] mb-2">50+</div>
            <div className="text-gray-300">Happy Clients</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-[#08CB00] mb-2">5+</div>
            <div className="text-gray-300">Years Experience</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-[#08CB00] mb-2">24/7</div>
            <div className="text-gray-300">Support Available</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;
