"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _cloneDeep = require("../util/cloneDeep");
const _configfull = /*#__PURE__*/ _interop_require_default(require("../../stubs/config.full"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const _default = (0, _cloneDeep.cloneDeep)(_configfull.default);
