import Navbar from './components/Navbar';

function App() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      {/* Main Content */}
      <div className="flex flex-col items-center justify-center p-8 pt-24">
        <h1 className="text-4xl font-bold text-[#08CB00] mb-4">Video Editor Portfolio</h1>
        <p className="text-lg text-gray-600 mb-8">Professional Video Editing Services</p>

        {/* Placeholder sections for the navbar links */}
        <div className="w-full max-w-4xl space-y-16">
          <section id="home" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Home Section</h2>
          </section>

          <section id="about" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">About Us Section</h2>
          </section>

          <section id="services" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Services Section</h2>
          </section>

          <section id="portfolio" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Portfolio Section</h2>
          </section>

          <section id="why-choose-us" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Why Choose Us Section</h2>
          </section>

          <section id="testimonials" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Testimonials Section</h2>
          </section>

          <section id="contact" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Contact Section</h2>
          </section>
        </div>
      </div>
    </div>
  )
}

export default App
